import { Controller } from "@hotwired/stimulus";

export default class extends Controller {
    static targets = ["dropdown", "preview", "hiddenInput"];

    connect() {
        this.closeDropdownBound = this.closeDropdown.bind(this);
        document.addEventListener("click", this.closeDropdownBound);
    }

    disconnect() {
        document.removeEventListener("click", this.closeDropdownBound);
    }

    // Přepíná zobrazení dropdownu
    toggle(event) {
        event.stopPropagation();
        this.dropdownTarget.classList.toggle("active");
    }

    // Při výběru ikony dekódujeme SVG z Base64, aktualizujeme skrytý input i náhled
    select(event) {
        event.stopPropagation();
        const button = event.currentTarget;
        const iconName = button.getAttribute("data-icon-picker-icon");
        const iconHtmlEncoded = button.getAttribute("data-icon-picker-icon-html");
        // Dekódujeme Base64 na původní HTML
        const iconHtml = atob(iconHtmlEncoded);

        // Nastavení hodnoty skrytého inputu
        this.hiddenInputTarget.value = iconName;
        // Aktualizace náhledu – kombinuje SVG a textový název

        this.previewTarget.innerHTML = iconHtml;
        // Zavření dropdownu
        this.dropdownTarget.classList.remove("active");
    }

    // Zruší výběr ikony
    clear(event) {
        event.stopPropagation();

        // Vymazání hodnoty skrytého inputu
        this.hiddenInputTarget.value = "";
        // Obnovení původního textu v náhledu
        this.previewTarget.innerHTML = "Vybrat ikonu";
        // Zavření dropdownu
        this.dropdownTarget.classList.remove("active");
    }

    // Zavře dropdown, pokud klikneme mimo komponentu
    closeDropdown(event) {
        if (!this.element.contains(event.target)) {
            this.dropdownTarget.classList.remove("active");
        }
    }
}